<template>
  <view class="personal-performance-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">个人业绩</view>
      <view class="nav-subtitle">{{ proxyInfo.relationship === "" ? proxy.userName : proxyInfo.relationship }}</view>
    </view>

    <!-- 时间导航栏 -->
    <view class="tab-navigation">
      <view class="tab-container">
        <view class="tab-item" :class="{ 'tab-active': checkNav === 0 }" @click="clickNav(0)">
          <view class="tab-text">日报</view>
          <view v-if="checkNav === 0" class="tab-indicator"></view>
        </view>
        <view class="tab-item" :class="{ 'tab-active': checkNav === 1 }" @click="clickNav(1)">
          <view class="tab-text">周报</view>
          <view v-if="checkNav === 1" class="tab-indicator"></view>
        </view>
        <view class="tab-item" :class="{ 'tab-active': checkNav === 2 }" @click="clickNav(2)">
          <view class="tab-text">月报</view>
          <view v-if="checkNav === 2" class="tab-indicator"></view>
        </view>
        <view class="tab-item" :class="{ 'tab-active': checkNav === 3 }" @click="clickNav(3)">
          <view class="tab-text" @click="showDatePicker">自定义</view>
          <view v-if="checkNav === 3" class="tab-indicator"></view>
        </view>
      </view>
    </view>

    <!-- 时间显示 -->
    <view class="date-display">{{ dateString }}</view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 完成订单卡片 -->
      <view class="order-card" @click="toOrderList('0')">
        <view class="order-header">
          <view class="order-title">
            <view class="title-text">已完成订单</view>
          </view>
          <view class="arrow-icon">›</view>
        </view>

        <view class="order-info">
          <view class="info-row">
            <view class="info-label">订单数</view>
            <view class="info-value">{{
              proxyInfo.completeCount === undefined
                ? "0"
                : proxyInfo.completeCount
            }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">金额数</view>
            <view class="info-value info-price">{{
              proxyInfo.allMoney === null ? "0.00" : proxyInfo.allMoney.toFixed(2)
            }}￥</view>
          </view>
        </view>

        <!-- 商品列表 -->
        <view v-if="proxyInfo.goods && proxyInfo.goods.length > 0" class="goods-section">
          <view class="goods-title">商品明细</view>
          <view class="goods-list">
            <view class="goods-item" v-for="(data, index) in proxyInfo.goods" :key="index">
              <view class="goods-name">{{ data.name }}</view>
              <view class="goods-count">{{ data.count }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 退货订单卡片 -->
      <view class="order-card" @click="toOrderList('1')">
        <view class="order-header">
          <view class="order-title">
            <view class="title-text">退货订单</view>
          </view>
          <view class="arrow-icon">›</view>
        </view>

        <view class="order-info">
          <view class="info-row">
            <view class="info-label">退货订单</view>
            <view class="info-value">{{ proxyInfo.FailCount || 0 }}</view>
          </view>
          <view class="info-row">
            <view class="info-label">退款金额</view>
            <view class="info-value info-highlight">{{
              proxyInfo.FailMoney === null
                ? "0.00"
                : proxyInfo.FailMoney.toFixed(2)
            }}￥</view>
          </view>
        </view>

        <!-- 退款明细 -->
        <view v-if="proxyInfo.refundOrders && proxyInfo.refundOrders.length > 0" class="refund-section">
          <view class="refund-title">退款明细</view>
          <view class="refund-list">
            <view class="refund-item" v-for="(data, index) in proxyInfo.refundOrders" :key="index">
              <view class="refund-info">
                <view class="refund-phone">手机号：{{ data.user.phnumber }}</view>
                <view class="refund-car">车牌号：{{ data.user.carId }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDateByDay } from "@/utils/dateUtil.js";
import { getInformationsByData } from "@/subpackages/proxy/api/proxyPerformance.js";
import { getUserId } from "@/utils/auth.js";

const getTime = function (str = "", flog) {
  if (flog === false) {
    return str.replaceAll("/", "-") + " 00:00:00";
  }
  return str.replaceAll("/", "-") + " 23:59:59";
};

const toLocaleDateString = function (data = new Date()) {
  let res = "";
  res += data.getFullYear() + "/";
  res += data.getMonth() + 1 + "/";
  res += data.getDate();
  return res;
};

export default {
  data() {
    return {
      checkNav: 0,
      range: ["", ""],
      proxy: {
        userName: "",
      },
      proxyInfo: {
        count: 0,
        FailMoney: 0,
        FailCount: 0,
        allMoney: 0,
        goods: [],
        Fail: [],
        relationship: "",
        finishOrders: [],
        refundOrders: [],
      },
    };
  },
  methods: {
    clickNav(index) {
      this.checkNav = index;
      if (index !== 3) {
        let start;
        let end = toLocaleDateString(getDateByDay(0));
        if (index === 0) {
          start = end;
        } else if (index === 1) {
          start = toLocaleDateString(getDateByDay(6));
        } else if (index === 2) {
          start = toLocaleDateString(getDateByDay(30));
        }
        this.range = [start, end];
        this.getData();
      }
    },
    getData() {
      console.log(this.proxy);
      uni.showLoading({
        title: "加载中",
      });
      const data = {
        userid: this.proxy.id,
        startTime: getTime(this.range[0], false),
        endTime: getTime(this.range[1], true),
      };
      getInformationsByData(data)
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          this.proxyInfo = res.data.data;
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    changeDate() {
      this.getData();
    },
    toOrderList(type) {
      if (this.proxy.id !== getUserId()) {
        return;
      }
      const data = {
        range: this.range.map((o) => {
          return o.replaceAll("/", "-");
        }),
        type,
      };
      uni.navigateTo({
        url: "/pages/proxyOrderList/orderList?data=" + JSON.stringify(data),
      });
    },
    // 显示日期选择器
    showDatePicker() {
      uni.showModal({
        title: "日期选择",
        content: "请选择日期范围（简化实现，实际项目中可使用原生picker）",
        confirmText: "确定",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            // 这里可以调用原生的日期选择器
            this.openNativeDatePicker();
          }
        },
      });
    },

    openNativeDatePicker() {
      // 使用原生日期选择器
      const date = new Date();
      const currentDate = `${date.getFullYear()}-${String(
        date.getMonth() + 1
      ).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;

      // 这里可以根据需要实现具体的日期选择逻辑
      console.log("打开原生日期选择器");
    },
  },
  onLoad(query) {
    const data = JSON.parse(query.data);
    this.proxy = data.proxy;
    const range = data.range;
    this.range = range;
    this.checkNav = data.checkNav;
    this.getData();
  },
  computed: {
    dateString() {
      let start = this.range[0];
      let end = this.range[1];
      console.log(start, end);
      start = start.replaceAll("-", "/");
      end = end.replaceAll("-", "/");
      const list1 = start.split("/");
      const list2 = end.split("/");
      let res = "";
      if (list1.length === 0) {
        return res;
      }
      res = list1[0] + "年" + list1[1] + "月" + list1[2] + "日";
      if (start === end) {
        return res;
      }
      res += " 至 " + list2[0] + "年" + list2[1] + "月" + list2[2] + "日";
      return res;
    },
  },
};
</script>

<style lang="scss">
/* 页面容器 */
.personal-performance-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 标签导航栏 */
.tab-navigation {
  background: white;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .tab-container {
    display: flex;
    justify-content: space-around;

    .tab-item {
      position: relative;
      padding: 16px 8px;
      flex: 1;
      text-align: center;
      transition: all 0.3s ease;

      .tab-text {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .tab-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        border-radius: 2px;
        animation: slideIn 0.3s ease;
      }

      &.tab-active {
        .tab-text {
          color: #00d4aa;
          font-weight: 600;
        }
      }
    }
  }
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }

  to {
    width: 30px;
    opacity: 1;
  }
}

/* 时间显示 */
.date-display {
  text-align: center;
  padding: 15px 20px;
  font-size: 14px;
  color: #666;
  background: white;
  margin: 0 20px 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 212, 170, 0.08);
  font-weight: 500;
}

/* 内容区域 */
.content-wrapper {
  padding: 0 20px 20px;
}

/* 订单卡片 */
.order-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }

  /* 订单头部 */
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 16px;
    border-bottom: 1px solid #f5f5f5;

    .order-title {
      display: flex;
      align-items: center;
      flex: 1;

      .title-text {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        line-height: 1.4;
      }
    }

    .arrow-icon {
      font-size: 20px;
      color: #00d4aa;
      font-weight: 600;
    }
  }

  /* 订单信息 */
  .order-info {
    padding: 16px 20px;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        min-width: 80px;
      }

      .info-value {
        font-size: 14px;
        color: #333;
        flex: 1;
        text-align: right;
        word-break: break-all;
      }

      .info-price {
        font-size: 16px;
        color: #00d4aa;
        font-weight: 600;
      }

      .info-highlight {
        font-size: 16px;
        color: #ff6b35;
        font-weight: 600;
      }
    }
  }

  /* 商品部分 */
  .goods-section {
    border-top: 1px solid #f5f5f5;
    padding: 16px 20px 20px;

    .goods-title {
      font-size: 14px;
      color: #666;
      font-weight: 500;
      margin-bottom: 12px;
    }

    .goods-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .goods-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(0, 212, 170, 0.1);
        border: 1px solid rgba(0, 212, 170, 0.2);
        border-radius: 12px;
        padding: 8px 12px;
        min-width: 120px;

        .goods-name {
          font-size: 13px;
          color: #00d4aa;
          font-weight: 500;
        }

        .goods-count {
          font-size: 13px;
          color: #00d4aa;
          font-weight: 600;
          margin-left: 8px;
        }
      }
    }
  }

  /* 退款部分 */
  .refund-section {
    border-top: 1px solid #f5f5f5;
    padding: 16px 20px 20px;

    .refund-title {
      font-size: 14px;
      color: #666;
      font-weight: 500;
      margin-bottom: 12px;
    }

    .refund-list {
      .refund-item {
        background: rgba(255, 107, 53, 0.1);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 12px;
        padding: 12px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .refund-info {

          .refund-phone,
          .refund-car {
            font-size: 13px;
            color: #ff6b35;
            font-weight: 500;
            margin-bottom: 4px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

.refund-phone,
.refund-car {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.refund-car {
  margin-bottom: 0;
}
</style>
