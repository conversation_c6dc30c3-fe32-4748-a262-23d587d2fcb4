<template>
  <view class="user-list-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">用户列表</view>
      <view class="nav-subtitle">管理您的用户信息</view>
    </view>

    <!-- 统计信息卡片 -->
    <view class="stats-card">
      <view class="stats-item">
        <view class="stats-value">{{ showList.length }}</view>
        <view class="stats-label">用户数</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-value">{{ mySum }} ￥</view>
        <view class="stats-label">消费额</view>
      </view>
    </view>

    <!-- 用户列表 -->
    <view class="content-wrapper">
      <view v-if="showList.length === 0" class="empty-state">
        <view class="empty-icon">👥</view>
        <view class="empty-text">{{ msg }}</view>
      </view>

      <view class="user-list">
        <view class="user-card" v-for="(data, index) in showList" :key="index">
          <!-- 用户头部 -->
          <view class="user-header">
            <view class="user-avatar">
              <image :src="data.headPic || '/static/photo.png'" class="avatar-image"></image>
            </view>
            <view class="user-basic-info">
              <view class="user-name">{{ data.userName }}{{ getRealName(data.realName) }}</view>
              <view class="user-date">注册时间：{{ formatTiem(data.createTime) }}</view>
            </view>
          </view>

          <!-- 用户详细信息 -->
          <view class="user-info">
            <view class="info-row">
              <view class="info-label">手机号</view>
              <view class="info-value">****{{ getPhoneTail(data.phone) }}</view>
            </view>
            <view class="info-row">
              <view class="info-label">车牌号</view>
              <view class="info-value">{{ getCaiId(data.carId) }}</view>
            </view>
            <view class="info-row">
              <view class="info-label">消费金额</view>
              <view class="info-value info-price">{{ formationMoney(data.money) }} ￥</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { myUser, myUserList } from "@/subpackages/proxy/api/proxyUserList.js";
import { getUserId, isLogin, isProxyLogin } from "@/utils/auth.js";
import { handlePagePermissionCheck } from "@/utils/permissionChecker.js";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      checkNav: 0,
      performanceObj: {},
      showList: [],
      msg: "加载中",
    };
  },
  methods: {
    formatTiem(timer = "") {
      return timer.split(" ")[0].replaceAll("/", "-");
    },
    getPhoneTail(phone = "") {
      return phone.substring(phone.length - 4);
    },
    getCaiId(carId) {
      if (carId == null) {
        return "/";
      }
      return carId;
    },
    getRealName(realName) {
      if (realName == null) {
        return "";
      }
      return " -- " + realName;
    },
    myUser() {
      myUserList()
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          console.log(res);
          const data = res.data.data;

          this.showList = data;
        })
        .finally(() => {
          this.msg = "暂无用户";
        });
    },
    formationMoney(money = 0) {
      if (money === null) {
        return "0.00";
      }
      return money.toFixed(2);
    },
  },
  computed: {
    ...mapGetters("proxyUser", ["userInfo"]),
    sum() {
      let sum = 0;
      this.showList.forEach((i) => {
        sum += i.money;
      });
      return sum;
    },
    mySum() {
      let sum = 0;
      this.showList.forEach((i) => {
        sum += i.money;
      });
      return sum.toFixed(2);
    },
  },
  onLoad() {
    // 严格的权限检查：基于adminRank的代理端页面权限验证
    if (!handlePagePermissionCheck("proxy", "代理端用户列表")) {
      return; // 权限检查失败，会自动重定向
    }
    this.myUser();
  },
};
</script>

<style lang="scss">
/* 页面容器 */
.user-list-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 统计信息卡片 */
.stats-card {
  background: white;
  margin: 20px;
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
  display: flex;
  justify-content: space-around;
  align-items: center;

  .stats-item {
    text-align: center;
    flex: 1;

    .stats-value {
      font-size: 24px;
      font-weight: 600;
      color: #00d4aa;
      margin-bottom: 8px;
      letter-spacing: 0.5px;
    }

    .stats-label {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }
  }

  .stats-divider {
    width: 1px;
    height: 50px;
    background: linear-gradient(180deg, transparent 0%, #e0e0e0 50%, transparent 100%);
    margin: 0 20px;
  }
}

/* 内容区域 */
.content-wrapper {
  padding: 0 20px 20px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 16px;
    color: #999;
    font-weight: 500;
  }
}

/* 用户列表 */
.user-list {
  .user-card {
    background: white;
    border-radius: 16px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    /* 用户头部 */
    .user-header {
      display: flex;
      align-items: center;
      padding: 20px 20px 16px;
      border-bottom: 1px solid #f5f5f5;

      .user-avatar {
        margin-right: 16px;

        .avatar-image {
          width: 60px;
          height: 60px;
          border-radius: 16px;
          border: 3px solid #00d4aa;
          object-fit: cover;
        }
      }

      .user-basic-info {
        flex: 1;

        .user-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 6px;
          line-height: 1.4;
        }

        .user-date {
          font-size: 13px;
          color: #666;
          font-weight: 500;
        }
      }
    }

    /* 用户信息 */
    .user-info {
      padding: 16px 20px 20px;

      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
          min-width: 80px;
        }

        .info-value {
          font-size: 14px;
          color: #333;
          flex: 1;
          text-align: right;
          word-break: break-all;
        }

        .info-price {
          font-size: 16px;
          color: #00d4aa;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
