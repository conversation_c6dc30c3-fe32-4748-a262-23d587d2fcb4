<template>
  <view class="bkcolor">
    <view class="index-top-wrap">
      <!-- 顶部功能栏 - 只保留用户端切换 -->
      <view class="top-header">
        <view class="top-functions">
          <!-- 切换到用户端 -->
          <view class="function-btn" @click="switchToUser">
            <view class="btn-circle">
              <text class="custom-icon" style="color: #4089fb; font-size: 16px">👤</text>
            </view>
            <text class="btn-text">用户端</text>
          </view>
        </view>
        <view class="header-title">代理端</view>
        <view class="placeholder"></view>
      </view>

      <!-- 用户信息卡片 -->
      <view class="user_info_box">
        <view class="user-avatar-section" @click="toUserInfoSet">
          <image v-if="userInfo.headPic === ''" :src="`${config.BASE_URL}/static/photo.png`" class="user-avatar">
          </image>
          <image v-else :src="userInfo.headPic" class="user-avatar"></image>
        </view>
        <view class="user-details" v-if="isLogin">
          <view class="user-name-section">
            <view class="user-name">{{ displayUserName }}</view>
          </view>
          <view class="user-phone">
            手机号：{{ formatPhone(userInfo.phone) }}
          </view>
          <view class="user-status">
            <view class="rank-badge">
              {{ rankDict[userInfo.adminRabk] || "代理商" }}
            </view>
          </view>
        </view>
        <view class="login-section" v-else>
          <view class="login-prompt" @click="toLogin">
            <text>登录</text>
            <text class="custom-icon" style="color: #333; font-size: 15px">›</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 第二排 -->
    <view class="wd index-second">
      <view v-for="(data, index) in navbar" :key="index" class="menu-item" @click="toUrl(data.url)">
        <view class="menu-icon">
          <image :src="data.icon"></image>
        </view>
        <view class="menu-text">{{ data.text }}</view>
      </view>
      <view @click="toProxyPerformance" class="menu-item">
        <view class="menu-icon">
          <image src="https://zk.yaoxuankeji.club:8199/images/2024/10/10/7634546c982b4d20b8bc0b0a2e491f61.png"></image>
        </view>
        <view class="menu-text">个人业绩</view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  isLogin,
  isProxyLogin,
  isLoginSimple,
  isProxyLoginSimple,
} from "@/utils/auth.js";
import { getDateByDay } from "@/utils/dateUtil.js";
import { mapGetters } from "vuex";
import { checkCurrentPageAccess } from "@/utils/routeGuard.js";
import { handlePagePermissionCheck } from "@/utils/permissionChecker.js";
import { logout } from "@/utils/logout.js";
import config from "@/utils/config.js";
const navbar = [
  {
    text: "用户列表",
    icon: "https://zk.yaoxuankeji.club:8199/images/2024/10/10/d2a8e7ba3a694b9fa4017702be5a3fda.png",
    url: "/subpackages/proxy/pages/proxyUserList/userList",
  },
  {
    text: "业绩管理",
    icon: "https://zk.yaoxuankeji.club:8199/images/2024/10/10/1cc328e92b7746018aa0063369945664.png",
    url: "/subpackages/proxy/pages/proxyPerformance/performance",
  },
  {
    text: "发展成员",
    icon: "https://zk.yaoxuankeji.club:8199/images/2024/10/10/e65008a16f5e48c1bb1184588dee3a3c.png",
    url: "/subpackages/proxy/pages/proxyDevelopment/development",
  },
];
const toLocaleDateString = function (data = new Date()) {
  let res = "";
  res += data.getFullYear() + "/";
  res += data.getMonth() + 1 + "/";
  res += data.getDate();
  return res;
};

export default {
  data() {
    return {
      config,
      topBarHeight: 0,
      isLogin: isLogin(),
      isProxyLogin: isProxyLogin(),
      navbar,
      rankDict: ["", "1级代理", "2级代理", "总代理"],
    };
  },
  onLoad(options) {
    // 检查页面访问权限
    if (!checkCurrentPageAccess()) {
      return; // 如果无权限访问，路由守卫会自动重定向
    }

    // 严格的权限检查：基于adminRank的代理端页面权限验证
    if (!handlePagePermissionCheck("proxy", "代理端首页")) {
      return; // 权限检查失败，会自动重定向
    }

    // 使用新的API获取状态栏高度    // #ifdef MP-WEIXIN
    const windowInfo = wx.getWindowInfo();
    this.topBarHeight = this.tabbarHeigth = windowInfo.statusBarHeight;
    // #endif

    // #ifndef MP-WEIXIN
    this.topBarHeight = this.tabbarHeigth =
      uni.getSystemInfoSync().statusBarHeight;
    // #endif
  },
  methods: {
    toUserInfoSet() {
      uni.navigateTo({
        url: "/subpackages/proxy/pages/proxySetUserInfo/setUserInfo",
      });
    },
    toLogin() {
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
    },
    toUrl(url) {
      uni.navigateTo({
        url,
      });
    },
    toProxyPerformance() {
      const data = {
        proxy: this.userInfo,
        checkNav: 0,
        range: [
          toLocaleDateString(getDateByDay(0)),
          toLocaleDateString(getDateByDay(0)),
        ],
      };
      uni.navigateTo({
        url:
          "/subpackages/proxy/pages/proxyProxyPerformance/proxyPerformance?data=" +
          JSON.stringify(data),
      });
    },
    // 格式化手机号，中间4位用*号隐藏
    formatPhone(phone) {
      if (!phone || phone.length !== 11) {
        return phone || "";
      }
      return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    },

    // 切换到用户端
    switchToUser() {
      uni.showModal({
        title: "切换到用户端",
        content: "将退出代理端登录并切换到用户端登录页面，是否继续？",
        confirmText: "继续",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            // 用户确认，退出登录并跳转到统一登录页面
            logout({
              showMessage: false,
            });

            // 延迟一点时间再跳转，确保登出完成
            setTimeout(() => {
              uni.reLaunch({
                url: "/pages/unifiedLogin/unifiedLogin",
              });
            }, 500);
          }
        },
      });
    },
  },
  onShow() {
    // 使用简化的登录状态检查，避免微信session检查导致的问题
    this.isLogin = isLoginSimple();
    this.isProxyLogin = isProxyLoginSimple();

    console.log("[ProxyIndex] 登录状态检查:", {
      isLogin: this.isLogin,
      isProxyLogin: this.isProxyLogin,
    });

    // 检查代理端登录状态
    if (!this.isLogin || !this.isProxyLogin) {
      console.log("用户未登录或非代理端登录，跳转到统一登录页");
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
      return;
    }

    // 只有在已登录状态下才获取用户信息
    if (this.isLogin) {
      // 获取代理端用户信息，如果失败也不影响页面显示
      this.$store.dispatch("proxyUser/getUserInfo").catch((error) => {
        console.log("获取代理端用户信息失败，但不影响页面使用:", error.message);
        // 可以显示默认的用户信息或提示用户刷新
      });
    }
  },
  computed: {
    ...mapGetters("proxyUser", ["userInfo"]),
    // 根据优先级显示用户名：realName > userName > phone
    displayUserName() {
      // 确保userInfo存在且不为空对象
      if (!this.userInfo || Object.keys(this.userInfo).length === 0) {
        return "加载中...";
      }

      if (
        this.userInfo.realName &&
        this.userInfo.realName !== null &&
        this.userInfo.realName.trim() !== "" &&
        this.userInfo.realName !== "null"
      ) {
        return this.userInfo.realName;
      }

      if (
        this.userInfo.userName &&
        this.userInfo.userName !== null &&
        this.userInfo.userName.trim() !== "" &&
        this.userInfo.userName !== "null"
      ) {
        return this.userInfo.userName;
      }

      if (this.userInfo.phone) {
        return this.userInfo.phone;
      }

      return "未设置昵称";
    },
  },
};
</script>

<style lang="scss" scoped>
.bkcolor {
  background: #f5f5f5;
  min-height: 100vh;
}

.index-top-wrap {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding-top: 50px;
  border-bottom-left-radius: 25px;
  border-bottom-right-radius: 25px;
  padding-bottom: 25px;
  position: relative;
}

/* 顶部功能栏样式 - 避开小程序右上角按钮 */
.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px 15px;

  .top-functions {
    display: flex;
    gap: 15px;
  }

  .header-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  .placeholder {
    width: 80px;
    /* 增加右侧预留空间，避开小程序菜单 */
  }
}

/* 顶部功能区样式 */
.function-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.btn-circle {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0.95);
  }
}

.btn-text {
  font-size: 11px;
  font-weight: 500;
  color: white;
  margin-top: 6px;
  letter-spacing: 0.3px;
}

.user_info_box {
  background: white;
  margin: 0 20px;
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;

  .user-avatar-section {
    margin-right: 20px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:active {
      transform: scale(0.95);
    }
  }

  .user-avatar {
    width: 70px;
    height: 70px;
    border-radius: 20px;
    border: 3px solid #00d4aa;
    transition: all 0.3s ease;
  }

  .user-details {
    flex: 1;

    .user-name-section {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }

    .user-name {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      letter-spacing: 0.3px;
    }

    .user-phone {
      font-size: 13px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .user-status {
      display: flex;
      align-items: center;
      gap: 12px;

      .rank-badge {
        font-size: 12px;
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        color: white;
        padding: 4px 10px;
        border-radius: 12px;
        font-weight: 500;
        letter-spacing: 0.2px;
      }
    }
  }

  .login-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    .login-prompt {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
      color: white;
      border-radius: 20px;
      font-size: 15px;
      font-weight: 500;
      transition: all 0.3s ease;
      letter-spacing: 0.3px;

      &:active {
        transform: scale(0.95);
      }

      text {
        color: white;
      }
    }
  }
}

.index-second {
  background: white;
  margin: 25px 20px;
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
  display: flex;
  justify-content: space-around;

  .menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    padding: 15px 8px;
    border-radius: 15px;
    transition: all 0.3s ease;

    &:active {
      background: rgba(0, 212, 170, 0.05);
      transform: scale(0.95);
    }

    .menu-icon {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 45px;
      height: 45px;
      background: linear-gradient(135deg,
          rgba(0, 212, 170, 0.1) 0%,
          rgba(0, 163, 137, 0.1) 100%);
      border-radius: 12px;

      image {
        width: 28px;
        height: 28px;
        object-fit: contain;
      }
    }

    .menu-text {
      font-size: 13px;
      color: #333;
      font-weight: 600;
      text-align: center;
      line-height: 1.2;
      letter-spacing: 0.2px;
    }
  }
}
</style>
